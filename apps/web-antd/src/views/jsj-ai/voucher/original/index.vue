<script setup lang="ts">


  import type {
    BankReceiptQueryParams,
    BankReceiptUpdateSceneParams,
    InvoiceQueryParams,
    InvoiceUpdateSceneParams,
    PayrollQueryParams,
  } from '#/api/jsj-ai/types';

  import { computed, onMounted, onUnmounted, ref, watch } from 'vue';

  import { message } from 'ant-design-vue';
  import dayjs from 'dayjs';

  import {
    updateBankReceiptScene,
    updateInvoiceScene,
  } from '#/api/jsj-ai/api-v2';
  import { useCompanySelection } from '#/hooks/jsj-ai/ai-chat/useCompanySelection';
  import { useColumnWidths } from '#/hooks/jsj-ai/voucher/useColumnWidths';
  import { useOriginalVoucherData } from '#/hooks/jsj-ai/voucher/useOriginalVoucherData';
  import {
    buildFileUrl as buildFileUrlUtil,
    buildInvoicePdfUrl,
  } from '#/utils/file/file-url';

  // 导入组件
  import ActionButtons from './components/ActionButtons.vue';
  import DataTable from './components/DataTable.vue';
  import FilterForm from './components/FilterForm.vue';
  import UpdateBankReceiptSceneModal from './components/UpdateBankReceiptSceneModal.vue';
  import UpdateSceneModal from './components/UpdateSceneModal.vue';
  // 导入配置和composables
  import {
    bankQuerySchema,
    getBankReceiptColumns,
    getInvoiceColumns,
    getPayrollColumns,
    invoiceQuerySchema,
    payrollQuerySchema,
  } from './data';

  // 标签页配置
  const tabList = [
    { key: 'output', label: '销项发票' },
    { key: 'input', label: '进项发票' },
    { key: 'bank', label: '银行回单' },
    { key: 'payroll', label: '工资单' },
  ];
  const activeTab = ref('output');

  // 使用composables
  const {
    clearSelectedInvoices,
    fetchBankReceiptData,
    fetchInvoiceData,
    fetchPayrollData,
    filterBankReceiptData,
    handlePageSizeChange,
    handlePaginationChange,
    loading,
    originalBankData,
    pagination,
    rowSelection,
    selectedInvoices,
    tableData,
  } = useOriginalVoucherData();

  const {
    handleResizeColumn,
    loadColumnWidths,
    resizeCounter,
    saveColumnWidths,
  } = useColumnWidths();

  // 全局公司状态管理
  const { selectedCompany, selectedMonth } = useCompanySelection();

  // 银行选项（基于实际数据动态生成）
  const bankOptions = computed(() => {
    const banks = new Set<string>();
    originalBankData.value.forEach((item) => {
      if (item.bank_name && item.bank_name.trim() !== '') {
        banks.add(item.bank_name);
      }
    });
    return [...banks].sort().map((bank) => ({
      label: bank,
      value: bank,
    }));
  });

  // 表单数据
  const formData = ref<Record<string, any>>({});

  // 动态schema
  const formSchema = computed(() => {
    if (activeTab.value === 'bank') {
      return bankQuerySchema(bankOptions.value);
    } else if (activeTab.value === 'payroll') {
      return payrollQuerySchema();
    }
    return invoiceQuerySchema();
  });

  // 表格列配置
  const antdColumns = ref<any[]>([]);
  const tableKey = computed(
    () => `${activeTab.value}-table-${resizeCounter.value}`,
  );

  // 更新场景相关状态
  const updateSceneModalVisible = ref(false);
  const updateBankReceiptSceneModalVisible = ref(false);
  const updateSceneLoading = ref(false);

  // PDF预览相关状态
  const pdfPreviewVisible = ref(false);
  const pdfPreviewUrl = ref('');
  const pdfPreviewTitle = ref('');
  const pdfPreviewFileType = ref<'image' | 'pdf' | 'unknown'>('unknown');

  // 生成列配置
  const updateColumns = () => {
    if (activeTab.value === 'bank') {
      antdColumns.value = getBankReceiptColumns(handleViewOriginalFile);
    } else if (activeTab.value === 'payroll') {
      antdColumns.value = getPayrollColumns();
    } else {
      antdColumns.value = getInvoiceColumns(
        activeTab.value as 'input' | 'output',
        handleViewOriginalFile,
      );
    }
    // 加载保存的列宽设置
    loadColumnWidths(antdColumns.value, activeTab.value);
  };

  // 重置表单数据
  const resetFormData = () => {
    const schema = formSchema.value;
    const newFormData: Record<string, any> = {};

    schema.forEach((item: any) => {
      newFormData[item.fieldName] =
        item.defaultValue === undefined ? undefined : item.defaultValue;
    });

    formData.value = newFormData;
  };

  // 查询数据的统一方法
  const fetchData = async () => {
    const values = formData.value;

    if (!selectedCompany.value) {
      message.warning('请选择公司名称');
      return;
    }

    if (activeTab.value === 'bank') {
      // 使用顶部月份选择框的值
      const monthValue = selectedMonth.value;

      const params: BankReceiptQueryParams = {
        company_name: selectedCompany.value,
        month: monthValue,
        type: values.type,
        voucher_num: values.voucher_num,
      };

      await fetchBankReceiptData(params);
      filterBankReceiptData(values);
    } else if (activeTab.value === 'payroll') {
      // 使用顶部月份选择框的值
      const monthValue = selectedMonth.value;

      if (!monthValue) {
        message.warning('请先选择月份');
        return;
      }

      const params: PayrollQueryParams = {
        company_name: selectedCompany.value,
        id_number: values.id_number,
        month: monthValue,
        name: values.name,
        voucher_num: values.voucher_num,
      };

      await fetchPayrollData(params);
    } else {
      // 将月份转换为对应的开始时间和结束时间
      const monthValue = selectedMonth.value;
      let begin_time: string | undefined;
      let end_time: string | undefined;

      if (monthValue) {
        // 将YYYYMM格式转换为日期范围
        const monthDate = dayjs(monthValue, 'YYYYMM');
        begin_time = monthDate.startOf('month').format('YYYY-MM-DD');
        end_time = monthDate.endOf('month').format('YYYY-MM-DD');
      }

      const params: InvoiceQueryParams = {
        begin_time,
        company_name: selectedCompany.value,
        end_time,
        input_output: activeTab.value as 'input' | 'output',
        status: values.status,
        voucher_num: values.voucher_num,
      };

      await fetchInvoiceData(params);
    }
  };

  // 查看原文件
  const handleViewOriginalFile = (record: any) => {
    const fileUrl = buildFileUrl(record);
    if (fileUrl === '#') {
      message.warning('该记录没有关联的原文件');
      return;
    }

    pdfPreviewUrl.value = fileUrl;
    pdfPreviewTitle.value = `原文件 - ${record.company_name || '未知公司'}`;
    pdfPreviewFileType.value = getFileType(fileUrl);
    pdfPreviewVisible.value = true;
  };

  // 构建文件URL
  const buildFileUrl = (row: any) => {
    const companyName = row.company_name || formData.value.company_name || '';
    if (!companyName) {
      console.warn('公司名称为空，无法构建文件URL');
      return '#';
    }

    if (row.digital_invoice_number) {
      return buildInvoicePdfUrl(
        row.digital_invoice_number,
        companyName,
        row.month,
      );
    }

    const url = row.source_file || row.url;
    if (url) {
      const isBankReceipt = activeTab.value === 'bank';
      return buildFileUrlUtil(row.url, companyName, isBankReceipt, row.month);
    }

    return '#';
  };

  // 检测文件类型
  const getFileType = (url: string): 'image' | 'pdf' | 'unknown' => {
    if (!url || url === '#') return 'unknown';

    const extension = url.split('.').pop()?.toLowerCase();
    if (
      ['bmp', 'gif', 'jpeg', 'jpg', 'png', 'webp'].includes(extension || '')
    ) {
      return 'image';
    }
    if (extension === 'pdf') {
      return 'pdf';
    }
    return 'unknown';
  };

  // 关闭PDF预览
  const closePdfPreview = () => {
    pdfPreviewVisible.value = false;
    pdfPreviewUrl.value = '';
    pdfPreviewTitle.value = '';
    pdfPreviewFileType.value = 'unknown';
  };

  // 下载文件
  const handleDownloadFile = () => {
    if (pdfPreviewUrl.value && pdfPreviewUrl.value !== '#') {
      window.open(pdfPreviewUrl.value, '_blank');
    }
  };

  // 处理字段变化
  const handleFieldChange = (fieldName: string, value: any) => {
    if (
      fieldName === 'company_name' &&
      value &&
      value !== selectedCompany.value
    ) {
      selectedCompany.value = value;
    }
  };

  // 重置按钮点击事件
  const handleReset = () => {
    resetFormData();
    clearSelectedInvoices();
    pagination.value.current = 1;
    fetchData();
    message.success('已重置搜索条件');
  };

  // 同步数据按钮
  const handleSyncData = () => {
    let dataTypes: string[] = [];
    switch (activeTab.value) {
      case 'input': {
        dataTypes = ['一般进项', '进项专票'];
        break;
      }
      case 'output': {
        dataTypes = ['销项发票'];
        break;
      }
      default: {
        dataTypes = ['销项发票', '一般进项', '进项专票'];
        break;
      }
    }

    const aiToggleEvent = new CustomEvent('ai-toggle-trigger', {
      detail: {
        action: 'sync-data',
        dataTypes,
        message: '请同步当前公司的最新数据',
      },
    });

    window.dispatchEvent(aiToggleEvent);
  };

  // AI记账按钮
  const handleAiAccounting = () => {
    if (activeTab.value !== 'input' && activeTab.value !== 'output') {
      console.warn('AI记账功能仅支持发票标签页');
      return;
    }

    let types: string[] = [];
    switch (activeTab.value) {
      case 'input': {
        types = ['进项专票', '一般进项'];
        break;
      }
      case 'output': {
        types = ['销项发票'];
        break;
      }
      default: {
        console.warn('未知的标签页类型:', activeTab.value);
        return;
      }
    }

    const aiToggleEvent = new CustomEvent('ai-toggle-trigger', {
      detail: {
        action: 'db-to-voucher',
        message: 'AI记账(资金场景识别)',
        types,
      },
    });

    window.dispatchEvent(aiToggleEvent);
  };

  // 银行回单同步
  const handleBankReceiptSync = () => {
    const aiToggleEvent = new CustomEvent('ai-toggle-trigger', {
      detail: {
        action: 'bank-receipt-sync',
        message: '同步银行回单',
      },
    });

    window.dispatchEvent(aiToggleEvent);
  };

  // 更新场景按钮
  const handleUpdateScene = () => {
    if (
      activeTab.value !== 'input' &&
      activeTab.value !== 'output' &&
      activeTab.value !== 'bank'
    ) {
      message.warning('只有发票和银行回单数据支持更新场景功能');
      return;
    }

    if (selectedInvoices.value.length === 0) {
      const dataType = activeTab.value === 'bank' ? '银行回单' : '发票';
      message.warning(`请先选择要更新场景的${dataType}`);
      return;
    }

    if (activeTab.value === 'bank') {
      updateBankReceiptSceneModalVisible.value = true;
    } else {
      updateSceneModalVisible.value = true;
    }
  };

  // 批量更新发票场景
  const batchUpdateInvoiceScene = async (sceneData: any) => {
    if (selectedInvoices.value.length === 0) {
      message.warning('没有选中的发票');
      return;
    }

    updateSceneLoading.value = true;
    try {
      const updateParams: InvoiceUpdateSceneParams[] =
        selectedInvoices.value.map((invoice) => ({
          company_name: formData.value.company_name || '',
          id: invoice._id,
          ii_buyer: sceneData.ii_buyer || '',
          ii_goods: sceneData.ii_goods || '',
          ii_note: sceneData.ii_note || '',
          ii_seller: sceneData.ii_seller || '',
          ii_tax_rates: sceneData.ii_tax_rates || 0,
          ii_type: sceneData.ii_type || 'normal',
          scene: sceneData.scene,
          type:
            activeTab.value === 'input' ? 'input_invoice' : 'output_invoice',
        }));

      const result = await updateInvoiceScene(updateParams);

      message.success(`成功更新 ${selectedInvoices.value.length} 条发票场景`);
      updateSceneModalVisible.value = false;
      clearSelectedInvoices();
      await fetchData();
    } catch (error: any) {
      console.error('更新发票场景失败:', error);
      message.error(error?.message || '更新发票场景失败');
    } finally {
      updateSceneLoading.value = false;
    }
  };

  // 批量更新银行回单场景
  const batchUpdateBankReceiptScene = async (sceneData: any) => {
    if (selectedInvoices.value.length === 0) {
      message.warning('没有选中的银行回单');
      return;
    }

    updateSceneLoading.value = true;
    try {
      const updateParams: BankReceiptUpdateSceneParams[] =
        selectedInvoices.value.map((bankReceipt) => ({
          br_account_name: sceneData.br_account_name || '',
          br_counterparty_account_name:
            sceneData.br_counterparty_account_name || '',
          br_currency: sceneData.br_currency || 'CNY',
          br_note: sceneData.br_note || '',
          br_summary: sceneData.br_summary || '',
          br_type: sceneData.br_type || '',
          company_name: formData.value.company_name || '',
          id: bankReceipt._id,
          scene: sceneData.scene,
          type: 'bank_receipt' as const,
        }));

      const result = await updateBankReceiptScene(updateParams);

      message.success(
        `成功更新 ${selectedInvoices.value.length} 条银行回单场景`,
      );
      updateBankReceiptSceneModalVisible.value = false;
      clearSelectedInvoices();
      await fetchData();
    } catch (error: any) {
      console.error('更新银行回单场景失败:', error);
      message.error(error?.message || '更新银行回单场景失败');
    } finally {
      updateSceneLoading.value = false;
    }
  };

  // 处理更新场景模态框取消事件
  const handleUpdateSceneCancel = () => {
    clearSelectedInvoices();
  };

  // 监听tab切换，重置表单数据
  watch(activeTab, (_newTab, oldTab) => {
    if (oldTab && antdColumns.value.length > 0) {
      saveColumnWidths(antdColumns.value, oldTab);
    }
    resetFormData();
    updateColumns();
    clearSelectedInvoices();
    pagination.value.current = 1;
    fetchData();
  });

  // 监听银行回单筛选条件变化，自动应用前端筛选
  watch(
    () => [formData.value.bank_name, formData.value.search_text],
    () => {
      if (activeTab.value === 'bank' && originalBankData.value.length > 0) {
        filterBankReceiptData(formData.value);
      }
    },
    { deep: true },
  );





  // 监听全局选中公司变化
  watch(
    selectedCompany,
    (newCompany: string) => {
      if (newCompany) {
        clearSelectedInvoices();
        pagination.value.current = 1;
        fetchData();
      }
    },
    { immediate: true },
  );

  // 页面初始化
  onMounted(async () => {
    try {
      resetFormData();
      updateColumns();
      await fetchData();
    } catch (error) {
      console.error('页面初始化失败:', error);
      resetFormData();
      updateColumns();
      fetchData();
    }

    // 添加数据刷新事件监听器
    const handleReloadData = () => {
      console.log('收到刷新原始凭证数据事件');
      fetchData();
    };

    window.addEventListener('reload-original-voucher-data', handleReloadData);

    // 组件卸载时移除事件监听器并保存列宽设置
    onUnmounted(() => {
      if (antdColumns.value.length > 0) {
        saveColumnWidths(antdColumns.value, activeTab.value);
      }
      window.removeEventListener(
        'reload-original-voucher-data',
        handleReloadData,
      );
    });
  });

  // 监听公司和月份变化，自动重新获取数据
  watch(
    [selectedCompany, selectedMonth],
    async ([newCompany, newMonth], [oldCompany, oldMonth]) => {
      // 只有在公司或月份真正发生变化时才重新获取数据
      if ((newCompany && newCompany !== oldCompany) || (newMonth && newMonth !== oldMonth)) {
        console.log('公司或月份发生变化，重新获取数据:', { newCompany, newMonth });
        await fetchData();
      }
    },
    { deep: true }
  );
</script>

<template>
  <div class="voucher-page-container">
    <!-- 整页大卡片 -->
    <a-card class="voucher-main-card">
      <!-- 头部区域：标签页 -->
      <div class="voucher-header">
        <a-tabs v-model:active-key="activeTab" class="voucher-tabs">
          <a-tab-pane v-for="tab in tabList" :key="tab.key" :tab="tab.label" />
        </a-tabs>
      </div>

      <!-- 筛选区域 -->
      <div class="voucher-filter-section">
        <FilterForm
          :schema="formSchema"
          v-model="formData"
          @submit="fetchData"
          @field-change="handleFieldChange"
        />
        <ActionButtons
          :active-tab="activeTab"
          :selected-count="selectedInvoices.length"
          @search="fetchData"
          @reset="handleReset"
          @sync-data="handleSyncData"
          @bank-receipt-sync="handleBankReceiptSync"
          @ai-accounting="handleAiAccounting"
          @update-scene="handleUpdateScene"
        />
      </div>

      <!-- 表格内容区域 -->
      <div class="voucher-table-container">
        <DataTable
          :columns="antdColumns"
          :data-source="tableData"
          :loading="loading"
          :row-selection="
            activeTab === 'input' ||
            activeTab === 'output' ||
            activeTab === 'bank'
              ? rowSelection
              : undefined
          "
          :pagination="pagination"
          :table-key="tableKey"
          :on-resize-column="
            (w, col) => handleResizeColumn(w, col, antdColumns, activeTab)
          "
          @pagination-change="handlePaginationChange"
          @page-size-change="handlePageSizeChange"
        />
      </div>
    </a-card>

    <!-- 更新场景模态框 -->
    <UpdateSceneModal
      v-model:visible="updateSceneModalVisible"
      :active-tab="activeTab"
      :loading="updateSceneLoading"
      :selected-count="selectedInvoices.length"
      :company-name="formData.company_name"
      @confirm="batchUpdateInvoiceScene"
      @cancel="handleUpdateSceneCancel"
    />

    <!-- 银行回单更新场景模态框 -->
    <UpdateBankReceiptSceneModal
      v-model:visible="updateBankReceiptSceneModalVisible"
      :active-tab="activeTab"
      :loading="updateSceneLoading"
      :selected-count="selectedInvoices.length"
      :company-name="formData.company_name"
      @confirm="batchUpdateBankReceiptScene"
      @cancel="handleUpdateSceneCancel"
    />

    <!-- 原文件预览模态框 -->
    <a-modal
      v-model:open="pdfPreviewVisible"
      :title="pdfPreviewTitle"
      width="80%"
      :footer="null"
      :centered="true"
      :destroy-on-close="true"
      @cancel="closePdfPreview"
    >
      <div class="file-preview-container">
        <!-- 图片预览 -->
        <div
          v-if="pdfPreviewFileType === 'image'"
          class="image-preview-wrapper"
        >
          <img
            :src="pdfPreviewUrl"
            :alt="pdfPreviewTitle"
            class="image-preview"
            @error="() => message.error('图片加载失败')"
          />
        </div>
        <!-- PDF预览 -->
        <iframe
          v-else-if="pdfPreviewFileType === 'pdf'"
          :src="pdfPreviewUrl"
          class="pdf-preview-iframe"
          frameborder="0"
        ></iframe>
        <!-- 未知文件类型 -->
        <div v-else class="unknown-file-preview">
          <div class="unknown-file-content">
            <a-result
              status="warning"
              title="无法预览此文件类型"
              sub-title="请下载文件后使用相应的应用程序打开"
            >
              <template #extra>
                <a-button type="primary" @click="handleDownloadFile">
                  下载文件
                </a-button>
              </template>
            </a-result>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped>
  @import './styles/index.scss';
</style>
