import type { ComputedRef } from 'vue';
import { ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { fetchScenarioEntryList } from '#/api/jsj-ai/api-v2';
import type { ScenarioEntryData, ScenarioEntryDetail } from '#/api/jsj-ai/types';

export interface SceneOption {
  label: string;
  value: string;
}

export interface CompanyNameRef {
  value: string | ComputedRef<string>;
}

export function useSceneOptions(companyNameRef: CompanyNameRef, sceneType?: string) {
  const sceneOptions = ref<SceneOption[]>([]);
  const loading = ref(false);

  // 获取场景选项
  async function fetchSceneOptions() {
    const companyName = typeof companyNameRef.value === 'string'
      ? companyNameRef.value
      : companyNameRef.value.value;

    if (!companyName) {
      sceneOptions.value = [];
      return;
    }

    try {
      loading.value = true;
      const params: any = {
        company_name: companyName,
        needDefault: 1 as 1,
        status: 1 as 1,
      };

      // 如果指定了场景类型，添加类型过滤
      if (sceneType) {
        params.type = sceneType;
      }

      const scenes = await fetchScenarioEntryList(params);
      // 提取唯一的场景名称
      const uniqueScenes = [
        ...new Set(scenes.map((item) => item.scene)),
      ].filter(Boolean);
      sceneOptions.value = uniqueScenes.map((scene) => ({
        label: scene,
        value: scene,
      }));
    } catch (error) {
      console.error('获取场景选项失败:', error);
      message.error('获取场景选项失败');
      sceneOptions.value = [];
    } finally {
      loading.value = false;
    }
  }

  // 监听公司名称变化
  watch(
    () => typeof companyNameRef.value === 'string'
      ? companyNameRef.value
      : companyNameRef.value.value,
    () => {
      fetchSceneOptions();
    },
    { immediate: true }, // 立即执行一次
  );

  return {
    sceneOptions,
    loading,
    fetchSceneOptions,
  };
}

// 获取场景详情的hook
export function useSceneDetails(companyNameRef: CompanyNameRef, sceneType?: string) {
  const sceneDetailsMap = ref<Map<string, ScenarioEntryDetail[]>>(new Map());
  const loading = ref(false);

  // 获取场景详情
  async function fetchSceneDetails(sceneName?: string) {
    const companyName = typeof companyNameRef.value === 'string'
      ? companyNameRef.value
      : companyNameRef.value.value;

    if (!companyName) {
      sceneDetailsMap.value.clear();
      return;
    }

    try {
      loading.value = true;
      const params: any = {
        company_name: companyName,
        needDefault: 1 as 1,
        status: 1 as 1,
      };

      // 如果指定了场景名称，只获取该场景的详情
      if (sceneName) {
        params.scene = sceneName;
      }

      // 如果指定了场景类型，添加类型过滤
      if (sceneType) {
        params.type = sceneType;
      }

      const scenes = await fetchScenarioEntryList(params);

      // 构建场景详情映射
      const detailsMap = new Map<string, ScenarioEntryDetail[]>();
      scenes.forEach((sceneData: ScenarioEntryData) => {
        if (sceneData.scene && sceneData.detail) {
          detailsMap.set(sceneData.scene, sceneData.detail);
        }
      });

      sceneDetailsMap.value = detailsMap;
    } catch (error) {
      console.error('获取场景详情失败:', error);
      message.error('获取场景详情失败');
      sceneDetailsMap.value.clear();
    } finally {
      loading.value = false;
    }
  }

  // 获取指定场景的详情
  function getSceneDetails(sceneName: string): ScenarioEntryDetail[] {
    return sceneDetailsMap.value.get(sceneName) || [];
  }

  return {
    sceneDetailsMap,
    loading,
    fetchSceneDetails,
    getSceneDetails,
  };
}

// 原始凭证页面专用的场景选项hook，支持根据tab页过滤
export function useOriginalVoucherSceneOptions(companyNameRef: CompanyNameRef, activeTabRef: { value: string | ComputedRef<string> }) {
  const sceneOptions = ref<SceneOption[]>([]);
  const loading = ref(false);

  // tab页与场景类型的映射关系
  const getSceneTypeByTab = (tab: string): string | undefined => {
    const tabTypeMap: Record<string, string> = {
      'input': '进项发票',
      'output': '销项发票',
      'bank': '银行回单',
      // 'payroll': 工资单暂时没有对应的场景类型
    };
    return tabTypeMap[tab];
  };

  // 获取场景选项
  async function fetchSceneOptions() {
    const companyName = typeof companyNameRef.value === 'string'
      ? companyNameRef.value
      : companyNameRef.value.value;

    if (!companyName) {
      sceneOptions.value = [];
      return;
    }

    const activeTab = typeof activeTabRef.value === 'string'
      ? activeTabRef.value
      : activeTabRef.value.value;
    const sceneType = getSceneTypeByTab(activeTab);

    // 如果当前tab不支持场景更新（如工资单），返回空选项
    if (!sceneType) {
      sceneOptions.value = [];
      return;
    }

    try {
      loading.value = true;
      const params: any = {
        company_name: companyName,
        needDefault: 1 as 1,
        status: 1 as 1,
        type: sceneType, // 根据当前tab过滤场景类型
      };

      const scenes = await fetchScenarioEntryList(params);
      // 提取唯一的场景名称
      const uniqueScenes = [
        ...new Set(scenes.map((item) => item.scene)),
      ].filter(Boolean);
      sceneOptions.value = uniqueScenes.map((scene) => ({
        label: scene,
        value: scene,
      }));
    } catch (error) {
      console.error('获取场景选项失败:', error);
      message.error('获取场景选项失败');
      sceneOptions.value = [];
    } finally {
      loading.value = false;
    }
  }

  // 监听公司名称和tab变化
  watch(
    [
      () => typeof companyNameRef.value === 'string'
        ? companyNameRef.value
        : companyNameRef.value.value,
      () => typeof activeTabRef.value === 'string'
        ? activeTabRef.value
        : activeTabRef.value.value
    ],
    () => {
      fetchSceneOptions();
    },
    { immediate: false }, // 不立即执行，避免初始化时的问题
  );

  return {
    sceneOptions,
    loading,
    fetchSceneOptions,
    getSceneTypeByTab,
  };
}
